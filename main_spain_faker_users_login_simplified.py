#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化优化版西班牙签证用户登录脚本
针对代理充足、单进程环境的优化版本
主要优化：
1. 线程安全的用户状态管理
2. 高效的并发处理
3. 智能的重复处理避免
"""

import sys
import time
import threading
from copy import deepcopy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Set
from collections import defaultdict
import json

from spain_visa_login import user_login
from extension.logger import logger
from extension.session_manager import create_session
from tool import get_new_proxy, get_random_user_agent
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, spain_faker_scaning
from user_manager import del_user_from_redis, get_user_info
from spain_visa_update_profile import update_user_profile, confirm_email

# 配置常量
DEFAULT_WORKER_COUNT = 20
LOGIN_TIMEOUT = 900  # 15分钟
BATCH_SIZE = 50
RETRY_DELAY = 2
PROCESS_DELAY = 1

class ThreadSafeUserManager:
    """线程安全的用户管理器"""
    
    def __init__(self):
        self._user_locks = defaultdict(threading.RLock)
        self._processing_users = set()
        self._lock = threading.RLock()
    
    def get_user_lock(self, user_key: str) -> threading.RLock:
        """获取用户专用锁"""
        return self._user_locks[user_key]
    
    def is_user_processing(self, user_key: str) -> bool:
        """检查用户是否正在处理中"""
        with self._lock:
            return user_key in self._processing_users
    
    def mark_user_processing(self, user_key: str, processing: bool = True):
        """标记用户处理状态"""
        with self._lock:
            if processing:
                self._processing_users.add(user_key)
            else:
                self._processing_users.discard(user_key)

class FastUserProcessor:
    """高效用户处理器"""
    
    def __init__(self, user_manager: ThreadSafeUserManager):
        self.user_manager = user_manager
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        self.stats_lock = threading.RLock()
    
    def update_stats(self, stat_type: str, increment: int = 1):
        """线程安全的统计更新"""
        with self.stats_lock:
            self.stats[stat_type] += increment
    
    def needs_relogin(self, user: Dict) -> bool:
        """检查用户是否需要重新登录"""
        if not user.get("is_login", False):
            return True
        
        time_now = int(time.time())
        update_time = int(user.get("updateTime", 0))
        return time_now - update_time > LOGIN_TIMEOUT
    
    def process_single_user(self, user: Dict) -> Optional[Dict]:
        """处理单个用户登录"""
        user_key = f"{user.get('centerCode')}-{user.get('passportNO')}"
        
        # 检查是否正在处理，避免重复处理
        if self.user_manager.is_user_processing(user_key):
            self.update_stats('skipped')
            return None
        
        # 获取用户锁
        user_lock = self.user_manager.get_user_lock(user_key)
        
        with user_lock:
            try:
                # 标记正在处理
                self.user_manager.mark_user_processing(user_key, True)
                
                # 获取最新用户信息
                fresh_user = get_user_info(user)
                if not fresh_user:
                    self.update_stats('skipped')
                    return None
                
                # 设置队列名称
                fresh_user["queue_name"] = spain_faker_scaning
                fresh_user.pop("headers", None)
                
                # 检查是否需要重新登录
                if not self.needs_relogin(fresh_user):
                    self.update_stats('skipped')
                    return fresh_user
                
                # 获取代理（代理充足，直接获取）
                proxy = get_new_proxy()
                if not proxy:
                    logger.warning(f"无法获取代理，跳过用户 {fresh_user.get('email')}")
                    self.update_stats('skipped')
                    return fresh_user
                
                # 创建会话
                header = deepcopy(headers)
                header["user-agent"] = get_random_user_agent()
                session = create_session(proxy=proxy, header=header)
                
                # 执行登录
                vip_label = "VIP" if fresh_user.get("acceptVIP") == 1 else "Normal"
                logger.debug(f"##正在登录##{fresh_user.get('email')}, {fresh_user.get('centerCode')}, {vip_label}")
                
                flag_login, info_login = user_login(fresh_user, session)
                
                # 更新用户状态
                updated_user = fresh_user.copy()
                updated_user["updateTime"] = int(time.time())
                
                if flag_login:
                    updated_user["is_login"] = True
                    updated_user["cookies"] = session.cookies.get_dict()
                    updated_user["proxy"] = proxy
                    logger.debug(f"##用户登录成功##: {fresh_user.get('email')}, {fresh_user.get('centerCode')} {vip_label}")
                    self.update_stats('success')
                    
                    # 处理用户工作流
                    updated_user = self.process_user_workflow(updated_user, session)
                else:
                    updated_user["is_login"] = False
                    if info_login == "deleted":
                        del_user_from_redis(fresh_user)
                        logger.error(f"##用户失效##{fresh_user.get('email')}, {fresh_user.get('centerCode')}, {vip_label}")
                        self.update_stats('failed')
                        return None
                    
                    logger.error(f"##用户登录失败##: {fresh_user.get('email')}, {fresh_user.get('centerCode')} {vip_label}")
                    self.update_stats('failed')
                
                # 保存用户状态
                save_user_2_redis_queue(updated_user)
                self.update_stats('processed')
                return updated_user
                
            except Exception as e:
                logger.error(f"处理用户 {user.get('email')} 时出错: {e}")
                self.update_stats('failed')
                return None
            finally:
                # 取消处理标记
                self.user_manager.mark_user_processing(user_key, False)
    
    def process_user_workflow(self, user: Dict, session) -> Dict:
        """处理用户工作流程"""
        try:
            # 更新用户信息
            if user["status"] == "login":
                user = self.process_profile_update(user, session)
            
            # 邮箱确认
            if user["status"] == "updated":
                user = self.process_email_confirmation(user, session)
        except Exception as e:
            logger.error(f"处理用户工作流失败 {user.get('email')}: {e}")
        
        return user
    
    def process_profile_update(self, user: Dict, session) -> Dict:
        """处理用户资料更新"""
        flag_profile, _ = update_user_profile(user, session)
        user_copy = user.copy()
        
        if flag_profile:
            logger.success(f"#注册# 更新成功:{user['email']} {user.get('visaTypeCode')}")
            user_copy["status"] = "updated"
        else:
            logger.error(f"#注册# 更新失败:{user['email']} {user.get('visaTypeCode')}")
        
        save_user_2_redis_queue(user_copy)
        return user_copy
    
    def process_email_confirmation(self, user: Dict, session) -> Dict:
        """处理邮箱确认"""
        confirm_flag, _ = confirm_email(user, session)
        user_copy = user.copy()
        
        if confirm_flag:
            logger.success(f"#注册# 确认邮箱成功:{user['email']} {user.get('visaTypeCode')}")
            user_copy["status"] = "update_appointment"
        else:
            logger.error(f"#注册# 确认邮箱失败:{user['email']}")
        
        save_user_2_redis_queue(user_copy)
        return user_copy

class FastUserLoginScanner:
    """高效用户登录扫描器"""
    
    def __init__(self, max_workers: int = DEFAULT_WORKER_COUNT):
        self.max_workers = max_workers
        self.user_manager = ThreadSafeUserManager()
        self.processor = FastUserProcessor(self.user_manager)
    
    def get_users_to_process(self) -> List[Dict]:
        """获取需要处理的用户"""
        try:
            all_faker_users_dict = get_users_with_queue_name(spain_faker_scaning)
            all_faker_users = []
            
            for field, user_json in all_faker_users_dict.items():
                try:
                    user = json.loads(user_json)
                    all_faker_users.append(user)
                except json.JSONDecodeError:
                    logger.error(f"解析用户数据失败: {field}")
                    continue
            
            # 如果是晚上8点后，处理所有用户
            if self.is_after_8pm():
                logger.warning("登录所有虚拟用户账号，保活")
            
            return all_faker_users
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
    
    def process_users_batch_fast(self, users: List[Dict]) -> List[Dict]:
        """高效批量用户处理"""
        if not users:
            return []
        
        logger.info(f"开始处理 {len(users)} 个用户")
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_user = {
                executor.submit(self.processor.process_single_user, user): user 
                for user in users
            }
            
            # 收集结果，设置合理的超时时间
            for future in as_completed(future_to_user, timeout=120):
                try:
                    result = future.result(timeout=30)
                    if result:
                        results.append(result)
                except Exception as e:
                    user = future_to_user[future]
                    logger.error(f"处理用户 {user.get('email')} 时出错: {e}")
        
        # 输出统计信息
        stats = self.processor.stats
        logger.info(f"批次处理完成 - 处理: {stats['processed']}, 成功: {stats['success']}, "
                   f"失败: {stats['failed']}, 跳过: {stats['skipped']}")
        
        # 重置统计信息
        with self.processor.stats_lock:
            self.processor.stats = {key: 0 for key in self.processor.stats}
        
        return results
    
    def is_after_8pm(self) -> bool:
        """判断当前时间是否是晚上八点之后"""
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute
        return current_hour > 20 or (current_hour == 20 and current_minute >= 30)
    
    def run_continuous_scan(self):
        """运行持续扫描"""
        logger.info(f"#扫号#高效版用户登录启动，工作线程数: {self.max_workers}")
        
        while True:
            try:
                # 获取需要处理的用户
                users_to_process = self.get_users_to_process()
                
                if not users_to_process:
                    logger.debug("没有用户需要处理，等待中...")
                    time.sleep(10)
                    continue
                
                # 分批处理用户
                for i in range(0, len(users_to_process), BATCH_SIZE):
                    batch = users_to_process[i:i + BATCH_SIZE]
                    self.process_users_batch_fast(batch)
                    
                    # 批次间延迟
                    if i + BATCH_SIZE < len(users_to_process):
                        time.sleep(PROCESS_DELAY)
                
                # 循环间延迟
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"扫描过程中出错: {e}")
                time.sleep(RETRY_DELAY)

def start_fast_scanning(thread_count: int = DEFAULT_WORKER_COUNT):
    """启动高效版用户登录扫描"""
    scanner = FastUserLoginScanner(max_workers=thread_count)
    scanner.run_continuous_scan()

if __name__ == "__main__":
    # 获取外部参数
    args = sys.argv[1:]
    thread_count = int(args[0]) if len(args) > 0 else DEFAULT_WORKER_COUNT
    start_fast_scanning(thread_count)
