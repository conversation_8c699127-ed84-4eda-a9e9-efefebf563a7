#!/bin/bash

echo "========================================"
echo "        Faker应用状态检查"
echo "========================================"

# 定义要检查的脚本列表
scripts=(
    "main_spain_faker_users_login.py"
    "main_spain_faker_users_appointment_v2.py"
    "main_spain_slot_cancel.py"
)

# 检查每个脚本的运行状态
echo "正在检查进程状态..."
echo ""

running_count=0
for script in "${scripts[@]}"; do
    echo "检查: $script"
    
    # 获取进程信息
    process_info=$(ps aux | grep "$script" | grep -v grep)
    
    if [ -z "$process_info" ]; then
        echo "  状态: ❌ 未运行"
    else
        echo "  状态: ✅ 正在运行"
        echo "  详情: $process_info"
        ((running_count++))
    fi
    echo ""
done

echo "========================================"
echo "运行状态汇总: $running_count/${#scripts[@]} 个应用正在运行"
echo "========================================"

# 检查日志文件
echo ""
echo "日志文件状态:"
log_files=(
    "logs_today/faker_login.log"
    "logs_today/faker_scaning.log"
    "logs_today/spain_user_slot_cancel.log"
)

for log_file in "${log_files[@]}"; do
    if [ -f "$log_file" ]; then
        file_size=$(du -h "$log_file" | cut -f1)
        last_modified=$(stat -c %y "$log_file" | cut -d'.' -f1)
        echo "  ✅ $log_file (大小: $file_size, 最后修改: $last_modified)"
    else
        echo "  ❌ $log_file (文件不存在)"
    fi
done

echo ""
echo "========================================"

# 如果有进程在运行，显示最近的日志
if [ $running_count -gt 0 ]; then
    echo "最近的扫描日志 (最后10行):"
    echo "----------------------------------------"
    if [ -f "logs_today/faker_scaning.log" ]; then
        tail -10 logs_today/faker_scaning.log
    else
        echo "扫描日志文件不存在"
    fi
    echo "----------------------------------------"
fi

echo ""
echo "使用说明:"
echo "  启动应用: ./start_faker_app.sh"
echo "  停止应用: ./stop_faker_app.sh"
echo "  查看状态: ./status_faker_app.sh"
echo "  查看日志: tail -f logs_today/faker_scaning.log"
